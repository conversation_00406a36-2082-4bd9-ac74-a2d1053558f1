<template>
  <div>
    <h2>Loading 测试页面</h2>
    
    <!-- 测试按钮 -->
    <div style="margin-bottom: 20px;">
      <el-button @click="testQuickLoading" type="primary">测试快速加载 (100ms)</el-button>
      <el-button @click="testSlowLoading" type="success">测试慢速加载 (2秒)</el-button>
      <el-button @click="testVerySlowLoading" type="warning">测试超慢加载 (5秒)</el-button>
    </div>
    
    <!-- 当前状态显示 -->
    <div style="margin-bottom: 20px;">
      <p>Loading 状态: <strong>{{ loading ? '加载中...' : '空闲' }}</strong></p>
      <p>数据条数: {{ tableData.length }}</p>
    </div>
    
    <!-- 测试表格 -->
    <el-table
      :data="tableData"
      :loading="loading"
      style="width: 100%"
      border
      height="400"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="email" label="邮箱" width="200" />
      <el-table-column prop="phone" label="电话" width="150" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'warning'">
            {{ scope.row.status === 1 ? '已审核' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const loading = ref(false);
const tableData = ref([]);

// 模拟数据生成
const generateMockData = (count: number = 10) => {
  const data = [];
  for (let i = 1; i <= count; i++) {
    data.push({
      id: i,
      name: `用户${i}`,
      email: `user${i}@example.com`,
      phone: `138${String(i).padStart(8, '0')}`,
      status: Math.random() > 0.5 ? 1 : 0,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString()
    });
  }
  return data;
};

// 测试快速加载 (100ms)
const testQuickLoading = async () => {
  console.log('开始快速加载测试...');
  loading.value = true;
  
  try {
    // 模拟快速 API 请求
    await new Promise(resolve => setTimeout(resolve, 100));
    tableData.value = generateMockData(5);
    console.log('快速加载完成');
  } catch (error) {
    console.error('加载失败:', error);
  } finally {
    loading.value = false;
  }
};

// 测试慢速加载 (2秒)
const testSlowLoading = async () => {
  console.log('开始慢速加载测试...');
  loading.value = true;
  
  try {
    // 模拟慢速 API 请求
    await new Promise(resolve => setTimeout(resolve, 2000));
    tableData.value = generateMockData(10);
    console.log('慢速加载完成');
  } catch (error) {
    console.error('加载失败:', error);
  } finally {
    loading.value = false;
  }
};

// 测试超慢加载 (5秒)
const testVerySlowLoading = async () => {
  console.log('开始超慢加载测试...');
  loading.value = true;
  
  try {
    // 模拟超慢 API 请求
    await new Promise(resolve => setTimeout(resolve, 5000));
    tableData.value = generateMockData(15);
    console.log('超慢加载完成');
  } catch (error) {
    console.error('加载失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
h2 {
  margin-bottom: 20px;
  color: #409eff;
}
</style>
