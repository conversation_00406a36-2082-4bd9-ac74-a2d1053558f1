export default {
  path: "/task",
  redirect: "/task/403",
  meta: {
    icon: "ri/information-line",
    title: "任务管理",
    rank: 11
  },
  children: [
    {
      path: "/task/management",
      meta: {
        title: "任务"
      },
      children: [
        {
          path: "/task/management/assignment",
          name: "task_assignment_awaiting_review",
          component: () => import("@/views/task_assignment/index.vue"),
          meta: {
            title: "任务分配",
            businessStatus: 10
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
