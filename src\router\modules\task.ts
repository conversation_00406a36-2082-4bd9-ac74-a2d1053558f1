const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/task",
    name: "Task",
    component: Layout,
    redirect: "/task/assignment",
    meta: {
      icon: "ri/information-line",
      title: "任务管理",
      rank: 11
    },
    children: [
      {
        path: "/task/assignment",
        name: "task_assignment_awaiting_review",
        component: () => import("@/views/task_assignment/index.vue"),
        meta: {
          title: "任务分配",
          businessStatus: 10
        }
      }
    ]
  },
  {
    path: "/task_assignment/detail",
    name: "TaskAssignmentDetail",
    component: () => import("@/views/task_assignment/details.vue"),
    meta: {
      title: "任务分配详情",
      icon: "ri/information-line",
      rank: 10,
      showLink: false
    }
  }
] satisfies RouteConfigsTable[];
