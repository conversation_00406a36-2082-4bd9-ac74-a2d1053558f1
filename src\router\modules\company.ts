export default {
  path: "/company",
  redirect: "/company/403",
  meta: {
    icon: "ri-building-2-fill",
    // showLink: false,
    title: "公司审批",
    rank: 10
  },
  children: [
    {
      path: "/company/settlement",
      meta: {
        title: "企业入驻"
      },
      children: [
        {
          path: "/company/settlement/awaiting_review",
          name: "settlement_awaiting_review",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/settlement/passed",
          name: "settlement_passed",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/settlement/rejected",
          name: "settlement_rejected",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/information",
      meta: {
        title: "企业信息"
      },
      children: [
        {
          path: "/company/information/awaiting_review",
          name: "information_awaiting_review",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/information/passed",
          name: "information_passed",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/information/rejected",
          name: "information_rejected",
          component: () =>
            import("@/views/company_Information/company_info/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/address",
      meta: {
        title: "地址认证"
      },
      children: [
        {
          path: "/company/address/awaiting_review",
          name: "address_awaiting_review",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/address/passed",
          name: "address_passed",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/address/rejected",
          name: "address_rejected",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/postInfo",
      meta: {
        title: "岗位认证"
      },
      children: [
        {
          path: "/company/postInfo/awaiting_review",
          name: "postInfo_awaiting_review",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/postInfo/passed",
          name: "postInfo_passed",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/postInfo/rejected",
          name: "postInfo_rejected",
          component: () =>
            import("@/views/company_Information/job_certification/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
