export default {
  path: "/company",
  redirect: "/company/403",
  meta: {
    icon: "ri-building-2-line",
    // showLink: false,
    title: "公司审批",
    rank: 10
  },
  children: [
    {
      path: "settlement",
      meta: {
        title: "企业入驻"
      },
      children: [
        {
          path: "awaiting_review",
          name: "settlement_awaiting_review",
          component: () => import("@/views/enterprise_settlement/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "passed",
          name: "settlement_passed",
          component: () => import("@/views/enterprise_settlement/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "rejected",
          name: "settlement_rejected",
          component: () => import("@/views/enterprise_settlement/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "information",
      meta: {
        title: "企业信息"
      },
      children: [
        {
          path: "awaiting_review",
          name: "information_awaiting_review",
          component: () => import("@/views/company_info/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "passed",
          name: "information_passed",
          component: () => import("@/views/company_info/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "rejected",
          name: "information_rejected",
          component: () => import("@/views/company_info/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "address",
      meta: {
        title: "地址认证"
      },
      children: [
        {
          path: "awaiting_review",
          name: "address_awaiting_review",
          component: () => import("@/views/address_verification/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "passed",
          name: "address_passed",
          component: () => import("@/views/address_verification/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "rejected",
          name: "address_rejected",
          component: () => import("@/views/address_verification/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "postInfo",
      meta: {
        title: "岗位认证"
      },
      children: [
        {
          path: "awaiting_review",
          name: "postInfo_awaiting_review",
          component: () => import("@/views/job_certification/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "passed",
          name: "postInfo_passed",
          component: () => import("@/views/job_certification/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "rejected",
          name: "postInfo_rejected",
          component: () => import("@/views/job_certification/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
