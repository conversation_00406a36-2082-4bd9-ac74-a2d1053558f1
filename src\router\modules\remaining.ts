const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录",
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/task_assignment/detail",
    name: "TaskAssignmentDetail",
    component: () => import("@/views/task_assignment/details.vue"),
    meta: {
      title: "任务分配详情",
      showLink: false,
      rank: 103
    }
  }
] satisfies Array<RouteConfigsTable>;
