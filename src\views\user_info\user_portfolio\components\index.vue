<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import VuePdfEmbed from "vue-pdf-embed";

const loading = ref<boolean>(false);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isAudit: {
    type: Boolean,
    default: false
  },
  avatarUrl: {
    type: String,
    default: ""
  },
  info: {
    type: Object,
    default: () => ({})
  }
});
// 控制弹窗内容的状态
const showResult = ref(false); // 结果页
const showTransfer = ref(false); // 转审页

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isAudit,
  newVal => {
    console.log("🚀 ~ newVal:", newVal);
    showResult.value = newVal;
  },
  { immediate: true }
);

const rightType = ref<"note" | "transfer" | "result">("note");

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 提交
const submit = async () => {
  // 这里可以添加提交逻辑
  $emit("cancelBtn");
};

function handlePass() {
  rightType.value = "result";
}
function handleReject() {
  rightType.value = "result";
}
function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  rightType.value = "note"; // 或者根据业务回到初始
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

// 示例数据，实际可用props传递
const info = {
  id: "ID:9712897189281",
  name: "张三钰",
  age: 26,
  gender: "男",
  phone: "19999999999",
  submitTime: "2024年6月9日 23:59"
};

const auditList = [
  { person: "张三", time: "2019.12.31 23:59", result: "通过" }
];
const statusList = [
  { person: "张三", time: "2019.12.31 23:59", person2: "张三" }
];
const info2 = {
  ...$props.info,
  auditTime: "2024年6月9日 23:59"
};

const noteOptions = [
  {
    label: "无批注",
    value: "none",
    content: ""
  },
  {
    label: "资料不全",
    value: "lack",
    content: "您的资料不全，请补充后重新提交。"
  }
];
const noteText = ref("");

const infoTableData = {
  userId: "ID:9712897189281",
  userName: "张三钰",
  age: 26,
  gender: "男",
  certName: "证书名称",
  userPhone: "19999999999",
  submitTime: "2024年6月9日 23:59"
};

const pdfUrl = "https://www.gjtool.cn/pdfh5/git.pdf";
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      @close="cancelBtn"
      align-center
    >
      <!-- 顶部信息卡片 -->
      <el-table
        :data="[infoTableData]"
        border
        class="info-table info-table-top"
        style="width: 100%; margin-bottom: 32px"
        :show-header="true"
        :header-cell-style="{
          background: '#fff',
          color: '#888',
          fontWeight: 500,
          fontSize: '16px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#fff',
          color: '#222',
          fontSize: '18px',
          fontWeight: 500,
          textAlign: 'center'
        }"
      >
        <el-table-column
          prop="userId"
          label="提交人ID"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column prop="userName" label="提交人姓名" min-width="100" />
        <el-table-column prop="age" label="年龄" min-width="80" />
        <el-table-column prop="gender" label="性别" min-width="80" />
        <el-table-column prop="certName" label="证书名称" min-width="140" />
        <el-table-column prop="userPhone" label="提交人电话" min-width="140" />
        <el-table-column prop="submitTime" label="提交时间" min-width="180" />
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧证书图片 -->
        <div class="card-box attachment-box">
          <div class="card-title">附件</div>
          <div class="pdf-preview-wrapper">
            <VuePdfEmbed v-if="pdfUrl" :source="pdfUrl" class="pdf-preview" />
            <div v-else class="no-pdf">暂无附件</div>
          </div>
        </div>
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              :transferList="transferList"
              v-model="transferValue"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'result'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard :options="noteOptions" v-model="noteText" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        >
        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-button type="success" @click="handlePass">通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
}
.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  margin-right: 24px;
}
.info-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.info-value {
  color: #222;
  font-size: 18px;
  font-weight: 500;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.attachment-box {
  .pdf-preview-wrapper {
    background: #f7f8fa;
    border-radius: 8px;
    width: 100%;
    height: 320px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .pdf-preview {
    width: 100%;
    height: 320px;
    border: none;
    background: #f7f8fa;
  }
  .no-pdf {
    color: #888;
    font-size: 16px;
    text-align: center;
    width: 100%;
  }
}
</style>
