export default {
  path: "/user",
  redirect: "/user/403",
  meta: {
    icon: "ri/information-line",
    // showLink: false,
    title: "用户审批",
    rank: 9
  },
  children: [
    {
      path: "/user/profile_picture",
      meta: {
        title: "头像"
      },
      children: [
        {
          path: "/user/profile_picture/awaiting_review",
          name: "profile_picture_awaiting_review",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/user/profile_picture/passed",
          name: "profile_picture_passed",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/user/profile_picture/rejected",
          name: "profile_picture_rejected",
          component: () => import("@/views/table/index.vue"),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/user/certificate",
      // name: "/user/certificate",
      // component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "证书"
      },
      children: [
        {
          path: "/user/certificate/awaiting_review",
          name: "certificate_awaiting_review",
          component: () => import("@/views/user_approval/index.vue"),
          meta: {
            title: "待审核"
          }
        },
        {
          path: "/user/certificate/passed",
          name: "certificate_passed",
          component: () => import("@/views/user_approval/index.vue"),
          meta: {
            title: "已通过"
          }
        },
        {
          path: "/user/certificate/rejected",
          name: "certificate_rejected",
          component: () => import("@/views/user_approval/index.vue"),
          meta: {
            title: "已驳回"
          }
        }
      ]
    },
    {
      path: "/user/appendix",
      // name: "/user/407",
      // component: () => import("@/views/user_approval/index.vue"),
      meta: {
        title: "附件"
      },
      children: [
        {
          path: "/user/appendix/awaiting_review",
          name: "appendix_awaiting_review",
          component: () => import("@/views/user_appendix/index.vue"),
          meta: {
            title: "待审核"
          }
        },
        {
          path: "/user/appendix/passed",
          name: "appendix_passed",
          component: () => import("@/views/user_appendix/index.vue"),
          meta: {
            title: "已通过"
          }
        },
        {
          path: "/user/appendix/rejected",
          name: "appendix_rejected",
          component: () => import("@/views/user_appendix/index.vue"),
          meta: {
            title: "已驳回"
          }
        }
      ]
    },
    {
      path: "/user/portfolio",
      meta: {
        title: "作品集"
      },
      children: [
        {
          path: "/user/portfolio/awaiting_review",
          name: "portfolio_awaiting_review",
          component: () => import("@/views/user_portfolio/index.vue"),
          meta: {
            title: "待审核"
          }
        },
        {
          path: "/user/portfolio/passed",
          name: "portfolio_passed",
          component: () => import("@/views/user_portfolio/index.vue"),
          meta: {
            title: "已通过"
          }
        },
        {
          path: "/user/portfolio/rejected",
          name: "portfolio_rejected",
          component: () => import("@/views/user_portfolio/index.vue"),
          meta: {
            title: "已驳回"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
