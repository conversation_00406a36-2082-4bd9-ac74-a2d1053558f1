<script lang="ts" setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";

// 表格数据
const tableData = ref([
  {
    name: "头像",
    executors: ["张三", "李四", "王正国"],
    time: "2024年12月31日 23:59",
    remain: 1999
  },
  {
    name: "简历",
    executors: [],
    time: "",
    remain: 0
  }
  // ... 你可以继续补充数据
]);

// 审批名称下拉选项（从数据中自动提取唯一name）
const nameOptions = computed(() => {
  return Array.from(new Set(tableData.value.map(item => item.name)));
});

// 筛选条件
const filter = ref({
  name: "",
  project: ""
});

// 监听筛选条件变化，进行筛选（这里只做了name筛选，project可根据实际需求扩展）
const filteredTableData = computed(() => {
  return tableData.value.filter(item => {
    const matchName = !filter.value.name || item.name === filter.value.name;
    // 这里假设item有project字段，如果没有可以忽略project筛选
    const matchProject =
      !filter.value.project ||
      (item.project && item.project.includes(filter.value.project));
    return matchName && matchProject;
  });
});

const router = useRouter();

// 查看详情
function handleView(row: any) {
  router.push({ name: "TaskAssignmentDetail", query: { id: row.id } });
}

// 分配任务
function handleAssign(row: any) {
  router.push({ name: "TaskAssignmentDetail", query: { id: row.id } });
}
</script>
<template>
  <div class="task-list-page">
    <div class="task-list-header">
      <span class="task-list-title">待审批列表表</span>
      <el-form :inline="true" class="task-list-form">
        <el-form-item label="审批名称">
          <el-select
            v-model="filter.name"
            placeholder="请输入项目名称"
            style="width: 260px"
          >
            <el-option
              v-for="item in nameOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="filteredTableData" border class="task-list-table">
      <el-table-column prop="name" label="名称" min-width="100" />
      <el-table-column prop="executors" label="执行" min-width="200">
        <template #default="scope">
          <template v-if="scope.row.executors && scope.row.executors.length">
            <span
              v-for="(user, idx) in scope.row.executors"
              :key="user"
              :class="['executor-tag', 'executor-tag-' + idx]"
              >{{ user }}</span
            >
          </template>
          <span v-else class="executor-none">无</span>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="时间" min-width="180" />
      <el-table-column prop="remain" label="剩余" min-width="80">
        <template #default="scope">
          <span class="remain-red">{{ scope.row.remain }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" min-width="120">
        <template #default="scope">
          <a class="status-link" @click="handleView(scope.row)">查看</a>
          <span class="status-assign" @click="handleAssign(scope.row)"
            >分配</span
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<style scoped lang="scss">
.task-list-page {
  background: #fff;
  border-radius: 10px;
  padding: 24px 24px 12px 24px;
  height: 85%;
}
.task-list-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 32px;
}
.task-list-title {
  font-size: 20px;
  font-weight: 700;
  color: #222;
  margin-bottom: 32px;
}
.task-list-form {
  width: 100%;
  .el-form-item {
    margin-bottom: 0;
  }
}
.task-list-table .el-table__cell {
  text-align: center;
  font-size: 15px;
}
.executor-tag {
  display: inline-block;
  min-width: 48px;
  margin-right: 6px;
  margin-bottom: 2px;
  border-radius: 6px;
  font-size: 14px;
  padding: 2px 12px;
  background: #e6f7e6;
  color: #1ecb8c;
}
.executor-tag-1 {
  background: #e6eaff;
  color: #277fff;
}
.executor-tag-2 {
  background: #c6f7f7;
  color: #1ecbcb;
}
.executor-none {
  color: #bbb;
}
.remain-red {
  color: #ff3c3c;
  font-weight: 600;
}
.status-link {
  color: #277fff;
  cursor: pointer;
  margin-right: 10px;
  font-weight: 500;
}
.status-assign {
  color: #1ecb8c;
  font-weight: 600;
  cursor: pointer;
}
</style>
