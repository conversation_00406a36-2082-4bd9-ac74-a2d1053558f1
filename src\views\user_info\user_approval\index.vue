<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { customList } from "@/utils/custom";
import TableDetails from "./components/index.vue";
import type { ComponentSize } from "element-plus";
import { getCertificateList } from "@/api/user_section/index";

const form = ref({});
const detailsDialogVisible = ref(false);
const isAudit = ref(false);
const route = useRoute();
const businessStatus = ref(0);
const total = ref(0);
const loading = ref(false);

let formQuery = [
  {
    type: "input",
    key: "username",
    label: "姓名"
  },
  {
    type: "input",
    key: "phone",
    label: "手机号"
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];

const params = ref({
  entity: {
    endTime: "",
    name: "",
    phone: "",
    startTime: "",
    status: null
  },
  orderBy: {},
  page: 1,
  size: 10
});

interface User {
  date: string;
  name: string;
  address: string;
}
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "name",
    label: "姓名",
    width: ""
  },
  {
    property: "age",
    label: "年龄",
    width: ""
  },
  {
    property: "sex",
    label: "性别",
    width: ""
  },
  {
    property: "phone",
    label: "手机号码",
    width: ""
  },
  {
    property: "name",
    label: "证书名称",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  }
];
const tableData = ref<any>([]);

const getCertificateListApi = async () => {
  loading.value = true;
  try {
    const res: any = await getCertificateList({ ...params.value });
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
      loading.value = false;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};
const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

function handleDetail(item) {
  detailsDialogVisible.value = true;
  isAudit.value = false;
}
const handleCancelBtn = () => {
  isAudit.value = false;
  detailsDialogVisible.value = false;
};

// 搜索按钮
const handleSearch = () => {
  getCertificateListApi();
};

// 重置按钮
const handleReset = () => {
  form.value = {};
  getCertificateListApi();
};

onMounted(() => {
  const statusParam = route.meta.businessStatus;
  params.value.entity.status =
    statusParam !== undefined ? Number(statusParam) : 0;
  businessStatus.value = statusParam !== undefined ? Number(statusParam) : 0;
  getCertificateListApi();
  customList.forEach(res => {
    console.log(res);
  });
  window.addEventListener("resize", resizeTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              size="large"
              :placeholder="'请输入' + item.label"
              clearable
            />
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="large"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button size="large" type="primary" @click="handleSearch"
            >搜索</el-button
          >
          <el-button
            size="large"
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        style="width: 100%"
        :loading="loading"
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) of tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          :property="config.property"
        />
        <el-table-column fixed="right" label="操作" min-width="60">
          <template #default="scope">
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleDetail(scope)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :isAudit="isAudit"
      @cancelBtn="handleCancelBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.billsplit-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  line-height: 60px;
}
.table-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 5px 20px;
  font-size: 14px;
  button {
    width: 74px !important;
    height: 40px !important;
  }
}
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
