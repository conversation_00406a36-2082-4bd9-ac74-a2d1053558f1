import { http } from "@/utils/http";

// 获取用户头像列表
const getUserSection = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userImageAudit/queryList", {
    data
  });
};

// 用户头像审批
const handleImageAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userImageAudit/audit", {
    data
  });
};

// 获取证书列表
const getCertificateList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/certificateAudit/queryList", {
    data
  });
};

export { getUserSection, handleImageAudit, getCertificateList };
