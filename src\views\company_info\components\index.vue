<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import { handleImageAudit } from "@/api/user_section/index";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isRightType,
  newVal => {
    console.log("🚀 ~ newVal:", newVal);
    rightType.value = newVal;
  },
  { immediate: true }
);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {
  const res = await handleImageAudit(params);
  if (res.code === 0) {
    ElMessage.success("操作成功");
    cancelBtn();
  }
};

// 通过
function handlePass(value: string) {
  const params = {
    id: infoTableData.value.id,
    status: value === "pass" ? 1 : 2
  };
  handleAudit(params);
  $emit("update:updataList", true);
}

// 驳回
function handleReject(value: string) {
  if (!noteText.value || noteText.value.trim() === "") {
    ElMessage.warning("请填写驳回原因");
    return;
  }
  const params = {
    id: infoTableData.value.id,
    status: value === "reject" ? 2 : 1,
    reason: noteText.value
  };
  handleAudit(params);
}

function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val) {
  // 这里处理转审逻辑
  cancelBtn();
}

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

const auditList = [
  { person: "张三", time: "2019.12.31 23:59", result: "通过" }
];
const statusList = [
  { person: "张三", time: "2019.12.31 23:59", person2: "张三" }
];

const noteOptions = [
  {
    label: "自定义批注",
    value: "none",
    content: ""
  },
  {
    label: "资料不合规",
    value: "lack",
    content: "您提交的资料不合规，请重新提交。"
  }
];
const noteText = ref("");

// 格式化时间戳函数
const formatTimestamp = (timestamp: number | string) => {
  if (!timestamp) return "";
  // 如果是字符串，尝试转换为数字
  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;
  // 检查是否为有效的时间戳（10位或13位）
  if (isNaN(time) || time <= 0) return "";
  // 如果是10位时间戳，转换为13位
  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const date = new Date(milliseconds);
  // 格式化日期时间
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

watch(
  () => $props.currentRow,
  newVal => {
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      @close="cancelBtn"
      align-center
    >
      <!-- 顶部信息区：两个el-table分两行，表头隐藏，内容上下分布 -->
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%; margin-bottom: 12px"
        class="info-table-normal"
      >
        <el-table-column prop="companyName" label="企业名称" min-width="180" />
        <el-table-column
          prop="creditCode"
          label="社会信用代码"
          min-width="180"
        />
        <el-table-column prop="legalPerson" label="法人" min-width="120" />
      </el-table>
      <el-table
        :data="[infoTableData]"
        border
        style="width: 100%"
        class="info-table-normal"
      >
        <el-table-column prop="id" label="ID" min-width="100">
          <template #default="scope"> ID: {{ scope.row.id }} </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" min-width="120" />
        <el-table-column prop="age" label="年龄" min-width="80" />
        <el-table-column prop="sex" label="性别" min-width="80">
          <template #default="scope">
            {{ scope.row.sex === 1 ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180" />
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧公司信息 -->
        <div class="company-info-card">
          <!-- 顶部：logo+公司名+规模+行业 -->
          <div class="company-info-header">
            <el-image
              v-if="infoTableData.logo"
              :src="infoTableData.logo"
              class="company-logo"
              fit="contain"
            />
            <div class="company-info-title">
              <div class="company-name">{{ infoTableData.companyName }}</div>
              <div class="company-tags">
                <span>{{ infoTableData.scale }}</span>
                <span>{{ infoTableData.industry }}</span>
              </div>
            </div>
          </div>
          <!-- 简介（可滚动） -->
          <div class="company-desc-scroll">
            {{ infoTableData.companyDesc }}
          </div>
          <!-- 福利标签 -->
          <div class="company-benefits">
            <el-tag
              v-for="(item, idx) in infoTableData.benefits"
              :key="idx"
              class="benefit-tag"
              type="info"
              effect="plain"
              >{{ item }}</el-tag
            >
          </div>
          <!-- 环境图片 -->
          <div class="company-env-imgs">
            <el-image
              v-for="(img, idx) in infoTableData.envImgs"
              :key="idx"
              :src="img"
              class="env-img"
              fit="cover"
              :preview-src-list="infoTableData.envImgs"
              :initial-index="idx"
            />
          </div>
        </div>
        <!-- 右侧内容保持不变 -->
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              :transferList="transferList"
              v-model="transferValue"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'result'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard v-model="noteText" :options="noteOptions" />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        >
        <el-button type="danger" @click="() => handleReject('reject')"
          >驳回</el-button
        >
        <el-button type="success" @click="() => handlePass('pass')"
          >通过</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
.company-info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 24px 24px 16px 24px;
  width: 100%;
  min-width: 340px;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.company-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.company-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 16px;
  background: #f5f6fa;
}
.company-info-title {
  display: flex;
  flex-direction: column;
}
.company-name {
  font-size: 18px;
  font-weight: 700;
  color: #222;
  margin-bottom: 4px;
}
.company-tags span {
  font-size: 13px;
  color: #888;
  margin-right: 12px;
}
.company-desc-scroll {
  max-height: 120px;
  overflow-y: auto;
  font-size: 14px;
  color: #444;
  margin-bottom: 12px;
  line-height: 1.7;
  width: 100%;
}
.company-benefits {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.benefit-tag {
  font-size: 13px;
  border-radius: 6px;
  padding: 2px 10px;
}
.company-env-imgs {
  display: flex;
  gap: 12px;
  width: 100%;
}
.env-img {
  width: 90px;
  height: 60px;
  border-radius: 8px;
  background: #f5f6fa;
  object-fit: cover;
}
</style>
