<script setup lang="ts">
const props = defineProps<{
  auditList: Array<{ person: string; time: string; result: string }>;
  statusList: Array<{ person: string; time: string; person2: string }>;
}>();
</script>

<template>
  <div class="audit-status-records-box">
    <div class="record-title">审核记录</div>
    <div class="scroll-container">
      <el-table
        :data="auditList"
        border
        class="audit-table"
        :show-header="true"
        :header-cell-style="{
          background: '#f7f8fa',
          color: '#888',
          fontWeight: 500,
          fontSize: '15px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#f7f8fa',
          color: '#222',
          fontSize: '15px',
          textAlign: 'center'
        }"
        style="width: 100%; margin-bottom: 24px"
      >
        <el-table-column prop="person" label="人员" min-width="80" />
        <el-table-column prop="time" label="审核时间" min-width="160" />
        <el-table-column prop="result" label="结果" min-width="80">
          <template #default="scope">
            <span
              :style="{
                color:
                  scope.row.result === '通过'
                    ? '#1FC600'
                    : scope.row.result === '驳回'
                      ? '#F56C6C'
                      : '#222'
              }"
            >
              {{ scope.row.result }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="record-title">状态记录</div>
      <el-table
        :data="statusList"
        border
        class="status-table"
        :show-header="true"
        :header-cell-style="{
          background: '#f7f8fa',
          color: '#888',
          fontWeight: 500,
          fontSize: '15px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#f7f8fa',
          color: '#222',
          fontSize: '15px',
          textAlign: 'center'
        }"
        style="width: 100%"
      >
        <el-table-column prop="person" label="人员" min-width="80" />
        <el-table-column prop="time" label="转审时间" min-width="160" />
        <el-table-column prop="person2" label="人员" min-width="80" />
      </el-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.audit-status-records-box {
  background: #fff;
  border-radius: 12px;
  padding: 24px 24px 18px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 380px;
  max-height: 350px;
  overflow: hidden; // 防止内容外溢
}

.scroll-container {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.record-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
}

.audit-table,
.status-table {
  background: #f7f8fa;
  border-radius: 8px;
  overflow: hidden;
}
</style>
